# DateTime JSON 转换器统一格式更改总结

## 问题描述
BackendModels 目录下的所有实体类中，与时间有关的属性在转换成JSON与后台服务通信时经常有冲突，造成报错。需要将所有的JSON转换格式固定成统一格式：`yyyy-MM-dd'T'HH:mm:ss.fffzzz`

## 解决方案
统一所有DateTime属性的JSON序列化/反序列化格式为：`yyyy-MM-dd'T'HH:mm:ss.fffzzz`

## 更改内容

### 1. 更新JSON转换器

#### DateTimeJsonConverter.cs
- 更改格式常量从 `"yyyy-MM-ddTHH:mm:ss.fffZ"` 到 `"yyyy-MM-dd'T'HH:mm:ss.fffzzz"`
- 改进读取逻辑，优先使用新格式，保持向后兼容
- 统一写入逻辑，保持时区信息

#### NullableDateTimeJsonConverter.cs
- 更改格式常量从 `"yyyy-MM-ddTHH:mm:ss.fffZ"` 到 `"yyyy-MM-dd'T'HH:mm:ss.fffzzz"`
- 改进读取逻辑，优先使用新格式，保持向后兼容
- 统一写入逻辑，保持时区信息

### 2. 新增配置提供器

#### JsonSerializerOptionsProvider.cs (新文件)
- 提供统一的JsonSerializerOptions配置
- 包含DefaultOptions、ApiOptions、DebugOptions三种预设
- 自动包含统一的DateTime转换器

### 3. 更新工具类

#### JsonDateTimeStringConverter.cs
- 更新为使用统一格式 `"yyyy-MM-dd'T'HH:mm:ss.fffzzz"`
- 添加DateTime和DateTime?两个重载方法
- 保持时区信息处理逻辑

### 4. 更新实体类

为以下实体类的所有DateTime属性添加了相应的JsonConverter特性：

#### TTask.cs
- `TaskCreatTime` - 添加 `[JsonConverter(typeof(DateTimeJsonConverter))]`
- `TaskNextRestartDateTime` - 添加 `[JsonConverter(typeof(NullableDateTimeJsonConverter))]`
- `DeletedTime` - 添加 `[JsonConverter(typeof(NullableDateTimeJsonConverter))]`

#### TaskReminder.cs
- `RemindTime` - 添加 `[JsonConverter(typeof(NullableDateTimeJsonConverter))]`
- `NextRemindTime` - 添加 `[JsonConverter(typeof(NullableDateTimeJsonConverter))]`

#### TaskTimeLog.cs
- `Date` - 添加 `[JsonConverter(typeof(DateTimeJsonConverter))]`
- `StartTime` - 添加 `[JsonConverter(typeof(DateTimeJsonConverter))]`
- `EndTime` - 添加 `[JsonConverter(typeof(NullableDateTimeJsonConverter))]`

#### TaskRestart.cs
- `TaskNextRestartDateTime` - 添加 `[JsonConverter(typeof(DateTimeJsonConverter))]`
- `FirstSettingRestartTime` - 添加 `[JsonConverter(typeof(NullableDateTimeJsonConverter))]`
- `CreatedAt` - 添加 `[JsonConverter(typeof(DateTimeJsonConverter))]`
- CreateTaskRestartRequest和UpdateTaskRestartRequest中的相应属性也添加了转换器

#### TaskList.cs
- `ListCreateTime` - 添加 `[JsonConverter(typeof(DateTimeJsonConverter))]`
- `ListCompleteTime` - 添加 `[JsonConverter(typeof(NullableDateTimeJsonConverter))]`
- `ListDueTime` - 添加 `[JsonConverter(typeof(NullableDateTimeJsonConverter))]`

#### TaskCategory.cs
- `CategoryCreateTime` - 添加 `[JsonConverter(typeof(DateTimeJsonConverter))]`
- `CategoryCompleteTime` - 添加 `[JsonConverter(typeof(NullableDateTimeJsonConverter))]`
- `CategoryDueTime` - 添加 `[JsonConverter(typeof(NullableDateTimeJsonConverter))]`
- `DeletedTime` - 添加 `[JsonConverter(typeof(NullableDateTimeJsonConverter))]`

#### TaskStep.cs
- `StepCompleteTime` - 添加 `[JsonConverter(typeof(NullableDateTimeJsonConverter))]`

#### ActiveTask.cs (DTOs)
- `ExpiresAt` - 添加 `[JsonConverter(typeof(DateTimeJsonConverter))]`

### 5. 更新注释规范
在所有相关文件中更新了JSON转换规范注释，明确指出：
- 日期统一使用 `yyyy-MM-dd'T'HH:mm:ss.fffzzz` 格式
- 使用统一的JsonConverter

### 6. 新增文档

#### README.md
- 详细说明了DateTime转换器的使用方法
- 提供了代码示例
- 说明了兼容性和注意事项

## 向后兼容性
转换器支持解析以下旧格式（向后兼容）：
- `yyyy-MM-ddTHH:mm:ss.fffZ` (原UTC格式)
- `yyyy-MM-ddTHH:mm:ss.fff` (原本地格式)
- `yyyy-MM-ddTHH:mm:ss`
- `yyyy-MM-dd`

## 使用建议
1. 在HTTP客户端配置中使用 `JsonSerializerOptionsProvider.ApiOptions`
2. 新的DateTime属性都应该添加相应的JsonConverter特性
3. 遵循项目中的JSON转换规范注释

## 测试建议
建议创建单元测试验证：
1. 新格式的序列化和反序列化
2. 向后兼容性
3. 时区信息的正确处理
4. 与后台服务的实际通信测试
