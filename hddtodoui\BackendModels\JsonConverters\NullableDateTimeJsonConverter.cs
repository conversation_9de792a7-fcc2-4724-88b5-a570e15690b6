using System;
using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace HddtodoUI.BackendModels.JsonConverters
{
     public class NullableDateTimeJsonConverter : JsonConverter<DateTime?>
    {
        private const string DateTimeFormat = "yyyy-MM-ddTHH:mm:ss.fffZ";

        public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Null)
            {
                return null;
            }

            var dateString = reader.GetString();
            if (string.IsNullOrEmpty(dateString))
                return null;

            // 尝试使用标准格式解析
            if (DateTime.TryParse(dateString, CultureInfo.InvariantCulture, DateTimeStyles.AssumeLocal, out var result))
                return result;

            // 如果标准解析失败，尝试使用指定格式解析
            try
            {
                // 解析为UTC时间，然后转换为本地时间
                var utcDate = DateTime.ParseExact(dateString, DateTimeFormat, CultureInfo.InvariantCulture, DateTimeStyles.AssumeLocal);
                return utcDate;
            }
            catch
            {
                // 如果解析失败，尝试其他常见格式
                string[] formats = { "yyyy-MM-ddTHH:mm:ss", "yyyy-MM-ddTHH:mm:ss.fff", "yyyy-MM-dd" };
                if (DateTime.TryParseExact(dateString, formats, CultureInfo.InvariantCulture, DateTimeStyles.AssumeLocal, out var parsedDate))
                    return parsedDate;

                // 如果所有尝试都失败，返回原始字符串的解析结果
                return DateTime.Parse(dateString, CultureInfo.InvariantCulture, DateTimeStyles.AssumeLocal);
            }
        }

        public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
        {
            if (!value.HasValue)
            {
                writer.WriteNullValue();
                return;
            }

            // 获取本地时间
            DateTime localTime = value.Value;
            if (localTime.Kind == DateTimeKind.Utc)
            {
                // 如果已经是UTC时间，转换为本地时间
                localTime = localTime.ToLocalTime();
            }
            else if (localTime.Kind == DateTimeKind.Unspecified)
            {
                // 如果未指定时区，假设是本地时间
                localTime = DateTime.SpecifyKind(localTime, DateTimeKind.Local);
            }

            // 序列化为ISO 8601格式的本地时间字符串
            writer.WriteStringValue(localTime.ToString("yyyy-MM-ddTHH:mm:ss.fff"));
        }
    }
}
