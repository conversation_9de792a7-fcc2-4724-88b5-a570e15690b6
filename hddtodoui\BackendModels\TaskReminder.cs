using System;
// JSON CONVERSION RULE (统一规范)
// 1. 所有共享枚举/日期使用 JsonPropertyName(全大写) 或 JsonConverters 目录统一实现。
// 2. 字段名必须与后端 JSON 字段完全一致，包括大小写、拼写（如 TaskPeriodSpanCount）。
// 3. 日期统一使用 ISO-8601 字符串；客户端默认 DateTime 解析，无需额外格式化。
// 4. 若需特殊序列化逻辑仅在此文件或 JsonConverters 中写一次。
// 5. 其他文件只引用现有 Converter/注解，禁止重复实现。

using System.Text.Json.Serialization;
using HddtodoUI.BackendModels.JsonConverters;

namespace HddtodoUI.BackendModels
{
    public class TaskReminder
    {

        public long ReminderId { get; set; }
        
        public long TaskId { get; set; }
        
        public long UserId { get; set; }

        public DateTime? RemindTime { get; set; }
    
        [JsonConverter(typeof(RemindRepeatTypeConverter))]
        public RemindRepeatType RepeatType { get; set; }
    
        public int RepeatInterval { get; set; }
    
        public DateTime? NextRemindTime { get; set; }

        public TaskReminder()
        {
            RepeatType = RemindRepeatType.None;
            RepeatInterval = 0;
        }

        public TaskReminder(long userId, long taskId)
        {
            UserId = userId;
            TaskId = taskId;
        }

        public DateTime? CaculateNextRemindTime()
        {
            DateTime? nextReminder = RemindTime;
            
            // 如果是重复提醒且提醒时间已过，计算下一次提醒时间
            if (RepeatType != RemindRepeatType.None && nextReminder < DateTime.Now)
            {
                // 计算从提醒时间到现在经过了多少个重复周期
                TimeSpan elapsed = DateTime.Now - nextReminder.Value;
                int cycles = 0;
                
                switch (RepeatType)
                {
                    case RemindRepeatType.Hourly:
                        cycles = (int)(elapsed.TotalHours / RepeatInterval) + 1;
                        nextReminder = nextReminder.Value.AddHours(cycles * RepeatInterval);
                        break;
                    case RemindRepeatType.Daily:
                        cycles = (int)(elapsed.TotalDays / RepeatInterval) + 1;
                        nextReminder = nextReminder.Value.AddDays(cycles * RepeatInterval);
                        break;
                    case RemindRepeatType.Weekly:
                        cycles = (int)(elapsed.TotalDays / (7 * RepeatInterval)) + 1;
                        nextReminder = nextReminder.Value.AddDays(cycles * 7 * RepeatInterval);
                        break;
                    case RemindRepeatType.Monthly:
                        int monthsPassed = (DateTime.Now.Year - nextReminder.Value.Year) * 12 + (DateTime.Now.Month - nextReminder.Value.Month);
                        cycles = (monthsPassed / RepeatInterval) + 1;
                        nextReminder = nextReminder.Value.AddMonths(cycles * RepeatInterval);
                        break;
                    case RemindRepeatType.Yearly:
                        int yearsPassed = DateTime.Now.Year - nextReminder.Value.Year;
                        cycles = (yearsPassed / RepeatInterval) + 1;
                        nextReminder = nextReminder.Value.AddYears(cycles * RepeatInterval);
                        break;
                }
            }

            return nextReminder;
        }

        public String GetRemindCycleDisplayInfo()
        {
            string repeatInfo = "";
            switch (RepeatType)
            {
                case RemindRepeatType.None:
                    repeatInfo = "一次性提醒";
                    break;
                case RemindRepeatType.Hourly:
                    repeatInfo = $"每{RepeatInterval}小时重复";
                    break;
                case RemindRepeatType.Daily:
                    repeatInfo = $"每{RepeatInterval}天重复";
                    break;
                case RemindRepeatType.Weekly:
                    repeatInfo = $"每{RepeatInterval}周重复";
                    break;
                case RemindRepeatType.Monthly:
                    repeatInfo = $"每{RepeatInterval}月重复";
                    break;
                case RemindRepeatType.Yearly:
                    repeatInfo = $"每{RepeatInterval}年重复";
                    break;
            }

            return repeatInfo;
        }
        
        public String GetNextRemindTimeDisplayInfo()
        {
            string repeatInfo = GetRemindCycleDisplayInfo();

            return   $"{NextRemindTime.ToString()} ({repeatInfo})";
            
        }
    }
    
   
}
