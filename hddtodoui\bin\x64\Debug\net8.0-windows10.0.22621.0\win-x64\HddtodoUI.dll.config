<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.2" />
    </startup>
    <appSettings>
        <add key="baseurl" value="http://localhost:9300/api" />
        <add key="toolTipShowGap" value="300" />
        <add key="username" value="hongdengdao" />
        <add key="password" value="123456" />
        <add key="IndicatorFormLeft" value="999" />
        <add key="IndicatorFormTop" value="1279" />
        <add key="IndicatorFormScreen" value="\\.\DISPLAY2" />
        <add key="IndicatorFormScreenBounds" value="0,0,2560,1440" />
        <add key="AutoHideMainForm" value="true" />
        <add key="ClientId" value="04bbdd8c-d6ae-44a5-a2e9-cf63a4028c5b" />
    </appSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Data.SQLite" publicKeyToken="db937bc2d44ff139" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*********" newVersion="*********" />
      </dependentAssembly>
        <dependentAssembly>
            <assemblyIdentity name="WinRT.Runtime" publicKeyToken="99ea127f02d97709" culture="neutral" />
            <bindingRedirect oldVersion="0.0.0.0-2.1.0.0" newVersion="2.2.0.0" />
        </dependentAssembly>
    </assemblyBinding>
      
  </runtime>
</configuration>