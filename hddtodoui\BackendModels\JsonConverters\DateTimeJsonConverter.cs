using System;
using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace HddtodoUI.BackendModels.JsonConverters
{
    /// <summary>
    /// 统一的DateTime JSON转换器，使用固定格式 yyyy-MM-dd'T'HH:mm:ss
    /// </summary>
    public class DateTimeJsonConverter : JsonConverter<DateTime>
    {
        // 统一使用的日期时间格式：yyyy-MM-dd'T'HH:mm:ss
        private const string DateTimeFormat = "yyyy-MM-dd'T'HH:mm:ss";

        public override DateTime Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            var dateString = reader.GetString();
            if (string.IsNullOrEmpty(dateString))
                return DateTime.MinValue;

            // 首先尝试使用统一格式解析
            if (DateTime.TryParseExact(dateString, DateTimeFormat, CultureInfo.InvariantCulture, DateTimeStyles.None, out var exactResult))
                return exactResult;

            // 尝试使用标准格式解析（兼容性）
            if (DateTime.TryParse(dateString, CultureInfo.InvariantCulture, DateTimeStyles.None, out var standardResult))
                return standardResult;

            // 如果标准解析失败，尝试其他常见格式（向后兼容）
            string[] fallbackFormats = {
                "yyyy-MM-dd'T'HH:mm:ss.fffzzz", // 原带时区格式
                "yyyy-MM-ddTHH:mm:ss.fffZ",     // 原UTC格式
                "yyyy-MM-ddTHH:mm:ss.fff",      // 原本地格式
                "yyyy-MM-ddTHH:mm:ss",
                "yyyy-MM-dd"
            };

            if (DateTime.TryParseExact(dateString, fallbackFormats, CultureInfo.InvariantCulture, DateTimeStyles.None, out var fallbackResult))
                return fallbackResult;

            // 最后尝试默认解析
            return DateTime.Parse(dateString, CultureInfo.InvariantCulture);
        }

        public override void Write(Utf8JsonWriter writer, DateTime value, JsonSerializerOptions options)
        {
            // 统一输出格式：yyyy-MM-dd'T'HH:mm:ss
            // 不包含毫秒和时区信息，使用本地时间
            writer.WriteStringValue(value.ToString(DateTimeFormat, CultureInfo.InvariantCulture));
        }
    }
}