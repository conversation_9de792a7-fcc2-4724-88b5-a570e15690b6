using System;
using System.Text.Json.Serialization;

// JSON CONVERSION RULE (统一规范)
// 1. 所有共享枚举/日期使用 JsonPropertyName(全大写) 或 JsonConverters 目录统一实现。
// 2. 字段名必须与后端 JSON 字段完全一致，包括大小写/拼写 (如 stepId)。
// 3. 日期统一使用 ISO-8601 字符串；客户端 DateTime 默认解析。
// 4. 若需特殊序列化逻辑，仅在此文件或 JsonConverters 中写一次。
// 5. 其它文件只引用现有 Converter/注解，禁止重复实现。

namespace HddtodoUI.BackendModels
{

    public class TaskStep
    {

        [JsonPropertyName("stepId")]
        public long StepId { get; set; }

        [JsonPropertyName("taskId")]
        public long TaskId { get; set; }

        [JsonPropertyName("userId")]
        public long UserId { get; set; }

        [JsonPropertyName("stepOrder")]
        public int StepOrder { get; set; }

        [JsonPropertyName("title")]
        public string Title { get; set; }

        [JsonPropertyName("color")]
        public string Color { get; set; }

        [JsonPropertyName("stepCompleteTime")]
        public DateTime? StepCompleteTime { get; set; }

        [JsonPropertyName("deletedStatus")]
        public bool DeletedStatus { get; set; }


        public TaskStep(long taskId, long userId, string title, string color)
        {
            this.TaskId = taskId;
            this.UserId = userId;
            this.Title = title;
            this.Color = color;
            this.StepCompleteTime = null;
            this.DeletedStatus = false;
        }

    }
}