# DateTime JSON 转换器使用说明

## 概述

为了解决与后台服务的时间格式转换冲突问题，项目中统一使用 `yyyy-MM-dd'T'HH:mm:ss` 格式进行DateTime的JSON序列化和反序列化。

## 统一格式

**标准格式**: `yyyy-MM-dd'T'HH:mm:ss`

示例：
- `2025-07-02T09:39:00` (标准格式，不包含毫秒和时区)
- `2024-12-25T15:30:45` (标准格式)

## 转换器类

### 1. DateTimeJsonConverter
用于非空DateTime属性的JSON转换。

### 2. NullableDateTimeJsonConverter  
用于可空DateTime?属性的JSON转换。

### 3. JsonSerializerOptionsProvider
提供预配置的JsonSerializerOptions，包含统一的DateTime转换器。

## 使用方法

### 在实体类中使用

```csharp
public class MyEntity
{
    [JsonConverter(typeof(DateTimeJsonConverter))]
    public DateTime CreatedTime { get; set; }
    
    [JsonConverter(typeof(NullableDateTimeJsonConverter))]
    public DateTime? UpdatedTime { get; set; }
}
```

### 在序列化/反序列化时使用

```csharp
// 使用统一的JsonSerializerOptions
var json = JsonSerializer.Serialize(entity, JsonSerializerOptionsProvider.DefaultOptions);
var entity = JsonSerializer.Deserialize<MyEntity>(json, JsonSerializerOptionsProvider.DefaultOptions);
```

### 使用工具类转换

```csharp
// 转换DateTime为字符串
string dateString = JsonDateTimeStringConverter.Convert(DateTime.Now);
string nullableDateString = JsonDateTimeStringConverter.Convert((DateTime?)null);
```

## 兼容性

转换器支持向后兼容，可以解析以下格式：
- `yyyy-MM-dd'T'HH:mm:ss` (新统一格式)
- `yyyy-MM-dd'T'HH:mm:ss.fffzzz` (原带时区格式)
- `yyyy-MM-ddTHH:mm:ss.fffZ` (原UTC格式)
- `yyyy-MM-ddTHH:mm:ss.fff` (原本地格式)
- `yyyy-MM-ddTHH:mm:ss`
- `yyyy-MM-dd`

## 注意事项

1. **简化格式**: 新格式不包含毫秒和时区信息，使用本地时间，符合后台服务要求。

2. **统一使用**: 所有BackendModels目录下的实体类DateTime属性都应该使用相应的JsonConverter特性。

3. **API通信**: 建议在HTTP客户端配置中使用JsonSerializerOptionsProvider.ApiOptions。

4. **调试**: 需要格式化输出时可使用JsonSerializerOptionsProvider.DebugOptions。

## 已更新的实体类

以下实体类的DateTime属性已经添加了统一的JsonConverter：

- `TTask.cs`
- `TaskReminder.cs`
- `TaskTimeLog.cs`
- `TaskRestart.cs`
- `ActiveTask.cs`

## 规范

遵循项目中的JSON转换规范注释：
1. 所有共享枚举/日期使用 JsonPropertyName 或 JsonConverters 目录统一实现
2. 字段名必须与后端 JSON 字段完全一致，包括大小写/拼写
3. 日期统一使用 yyyy-MM-dd'T'HH:mm:ss 格式；使用统一的JsonConverter
4. 若需特殊序列化逻辑，仅在此文件或 JsonConverters 中写一次
5. 其它文件只引用现有 Converter/注解，禁止重复实现
