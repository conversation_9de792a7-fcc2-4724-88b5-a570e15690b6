using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Text.Json.Serialization;
using HddtodoUI.BackendModels.JsonConverters;
using HddtodoUI.Services;

namespace HddtodoUI.BackendModels.BackendStore.HttpStore
{
    public class HttpTaskStore : HttpStoreBase, ITaskStore
    {
        private string BaseUrl = baseUrl;

        public HttpTaskStore(HttpClient httpClient = null) : base(httpClient, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Converters =
            {
                new DateTimeJsonConverter(),
                new NullableDateTimeJsonConverter(),
                new TaskGroupJsonConverter()
            }
        })
        {
        }

        public List<TTask> getRecentRunningTask(int count, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/recent/{userId}?count={count}";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Getting recent running tasks").Result;
        }

        public List<TTask> getAllUnCompleteTaskByBelongToTaskList(TaskCategory tl, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/uncomplete/list/{tl.Key}/{userId}";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Getting uncomplete tasks").Result;
        }

        public List<TTask> getAllUnCompleteTaskByDueByDate(DateTime dueDate, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/uncomplete/due/{userId}?date={dueDate:yyyy-MM-ddTHH:mm:ss}";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Getting uncomplete tasks by due date").Result;
        }

        public long getAllUnCompleteTaskCountsByDueByDate(DateTime dueDate, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/uncomplete/due/count/{userId}?date={dueDate:yyyy-MM-ddTHH:mm:ss}";
            return SendGetRequestAsync<long>(endpoint, "Getting uncomplete tasks count").Result;
        }

        public List<TTask> getAllUnCompleteTaskByDueBetweenDate(DateTime startDueDate, DateTime endDueDate, long userId)
        {
            var endpoint =
                $"{BaseUrl}/tasks/uncomplete/due/between/{userId}?startDate={startDueDate:yyyy-MM-ddTHH:mm:ss}&endDate={endDueDate:yyyy-MM-ddTHH:mm:ss}";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Getting uncomplete tasks between dates").Result;
        }

        public long getAllUnCompleteTaskCountsByDueBetweenDate(DateTime startDueDate, DateTime endDueDate, long userId)
        {
            var endpoint =
                $"{BaseUrl}/tasks/uncomplete/due/between/count/{userId}?startDate={startDueDate:yyyy-MM-ddTHH:mm:ss}&endDate={endDueDate:yyyy-MM-ddTHH:mm:ss}";
            return SendGetRequestAsync<long>(endpoint, "Getting uncomplete tasks count between dates").Result;
        }

        public TTask getTaskById(long taskId, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/{taskId}/{userId}";
            return SendGetRequestAsync<TTask>(endpoint, "Getting task").Result;
        }

        public IEnumerable<IGrouping<DateTime, TTask>> getGroupByDateCompleteTasks(TaskCategory taskList, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/complete/grouped/{taskList.Key}/{userId}";
            var groupedTasks = SendGetRequestAsync<List<TaskGroup>>(endpoint, "Getting grouped completed tasks").Result;
            return groupedTasks.Select(g => new TaskGrouping(g.Date, g.Tasks));
        }

        public IEnumerable<IGrouping<DateTime, TTask>> getGroupByTodayCompleteTasks(long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/complete/today/grouped/{userId}";
            var groupedTasks = SendGetRequestAsync<List<TaskGroup>>(endpoint, "Getting today's grouped completed tasks")
                .Result;
            return groupedTasks.Select(g => new TaskGrouping(g.Date, g.Tasks));
        }

        public TTask createTask(string title, TaskPriority priority, long userId, TaskCategory list, string parentTaskIds = null)
        {
            var endpoint = $"{BaseUrl}/tasks/create/{list.Key}/{userId}";
            var request = new CreateTaskRequest
            {
                Title = title,
                Priority = priority,
                ParentTaskIds = parentTaskIds
            };
            return SendPostRequestAsync<TTask>(endpoint, "Creating task", request).Result;
        }

        public TTask createTask(string title, TaskPriority priority, DateTime? dueTime, long userId, TaskCategory list, string parentTaskIds = null)
        {
            var endpoint = $"{BaseUrl}/tasks/create/due/{list.Key}/{userId}";
            var request = new CreateTaskWithDueRequest
            {
                Title = title,
                Priority = priority,
                DueTime = dueTime,
                ParentTaskIds = parentTaskIds
            };
            return SendPostRequestAsync<TTask>(endpoint, "Creating task with due time", request).Result;
        }

        public void addTaskToUncompleteList(TTask task, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/{task.TaskID}/uncomplete/{userId}";
            SendPutRequestAsync(endpoint, "Adding task to uncomplete list").Wait();
        }

        public void addTaskToCompleteList(TTask task, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/{task.TaskID}/complete/{userId}";
            SendPutRequestAsync(endpoint, "Adding task to complete list").Wait();
        }

        public List<TTask> getPagedCompleteTasks(string listKey, long userId, int page, int pageSize)
        {
            var endpoint = $"{BaseUrl}/tasks/complete/paged/{listKey}/{userId}?page={page}&pageSize={pageSize}";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Getting paged completed tasks").Result;
        }

        public long getCompleteTasksCount(string listKey, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/complete/count/{listKey}/{userId}";
            return SendGetRequestAsync<long>(endpoint, "Getting completed tasks count").Result;
        }

        public List<TTask> getTodayCompleteTasks(long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/complete/today/{userId}";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Getting today's completed tasks").Result;
        }

        public void saveTaskChange(TTask task, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/change/{userId}";
            LogService.Instance.Debug("duetime"+task.TaskDueTime);
            SendPutRequestAsync(endpoint, "Saving task changes", task).Wait();
        }

        public bool switchTaskToUncompleteList(TTask task, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/{task.TaskID}/uncomplete/{userId}";
            return SendPutRequestAsync<bool>(endpoint, "Switching task to uncomplete list").Result;
        }

        public bool switchTaskToCompleteList(TTask task, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/{task.TaskID}/complete/{userId}";
            return SendPutRequestAsync<bool>(endpoint, "Switching task to complete list").Result;
        }
        
        public void moveTaskToOtherTaskList(TTask source, TaskCategory target, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/{source.TaskID}/move/{target.Key}/{userId}";
            SendPutRequestAsync(endpoint, "Moving task to other list").Wait();
        }

        public void dragUncompleteTask(long startId, long targetId, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/{startId}/drag/{targetId}/{userId}";
            SendPutRequestAsync(endpoint, "Dragging uncomplete task").Wait();
        }

        public string GetTaskTitle(long taskId, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/{taskId}/title/{userId}";
            return SendGetRequestAsync<string>(endpoint, "Getting task title").Result;
        }

        public List<TTask> getTaskListByKeyword(string keyword, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/search/{userId}?keyword={Uri.EscapeDataString(keyword)}&isAll=false";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Searching tasks").Result;
        }

        public List<TTask> getTaskListByKeyword(string keyword, bool isAll, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/search/{userId}?keyword={Uri.EscapeDataString(keyword)}&isAll={isAll}";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Searching tasks").Result;
        }

        public TTask getLastExecutionUncompletedTask(long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/last/uncompleted/{userId}";
            return SendGetRequestAsync<TTask>(endpoint, "Getting last execution uncompleted task").Result;
        }

        public List<TTask> getWaitToComputeRestartTimeTaskList(long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/restart/compute/{userId}";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Getting tasks waiting to compute restart time").Result;
        }

        public List<TTask> getWaitToRestartTaskList(long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/restart/ready/{userId}";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Getting tasks waiting to restart").Result;
        }

        public List<TTask> GetTasksWithReminders(long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/reminders/{userId}";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Getting tasks with reminders").Result;
        }

        public List<TTask> getAllUnCompleteHighPriorityTasks(long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/uncomplete/highpriority/{userId}";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Getting uncomplete high priority tasks").Result;
        }

        public long getAllUnCompleteHighPriorityTasksCount(long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/uncomplete/highpriority/count/{userId}";
            return SendGetRequestAsync<long>(endpoint, "Getting uncomplete high priority tasks count").Result;
        }

        public long getDeletedTasksCount(long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/deleted/count/{userId}";
            return SendGetRequestAsync<long>(endpoint, "Getting deleted tasks count").Result;
        }

        public List<TTask> getPagedDeletedTasks(long userId, int page, int pageSize)
        {
            var endpoint = $"{BaseUrl}/tasks/deleted/paged/{userId}?page={page}&pageSize={pageSize}";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Getting paged deleted tasks").Result;
        }

        public void removeTask(long taskId, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/remove/{userId}/{taskId}";
            SendDeleteRequestAsync(endpoint, "Removing task").Wait();
        }

        public void unremoveTask(long taskId, long userId)
        {
            var endpoint = $"{BaseUrl}/tasks/unremove/{userId}/{taskId}";
            SendPutRequestAsync(endpoint, "Unremoving task", null).Wait();
        }

        public List<TTask> getSubTasksByParentIds(long userId, string parentIds)
        {
            var endpoint = $"{BaseUrl}/tasks/subtasks/byParentIds/{userId}/{parentIds}";
            return SendGetRequestAsync<List<TTask>>(endpoint, "Getting subtasks by parent IDs").Result;
        }

        public long updateSubtaskCount(long userId, long taskId)
        {
            var endpoint = $"{BaseUrl}/tasks/update-subtask-count/{userId}/{taskId}";
            var response = SendPutRequestAsync<SubtaskCountResponse>(endpoint, "Updating subtask count").Result;
            return response.Count;
        }

        private class SubtaskCountResponse
        {
            public long Count { get; set; }
        }

        // Custom JSON converter for TaskGroup array format
        private class TaskGroupJsonConverter : JsonConverter<List<TaskGroup>>
        {
            public override List<TaskGroup> Read(ref Utf8JsonReader reader, Type typeToConvert,
                JsonSerializerOptions options)
            {
                if (reader.TokenType != JsonTokenType.StartArray)
                    throw new JsonException("Expected start of array");

                var result = new List<TaskGroup>();
                reader.Read(); // Move to first array element

                while (reader.TokenType != JsonTokenType.EndArray)
                {
                    if (reader.TokenType != JsonTokenType.StartArray)
                        throw new JsonException("Expected start of inner array");

                    reader.Read(); // Move to date string
                    if (reader.TokenType != JsonTokenType.String)
                        throw new JsonException("Expected date string");

                    var date = DateTime.Parse(reader.GetString());
                    reader.Read(); // Move to tasks array

                    if (reader.TokenType != JsonTokenType.StartArray)
                        throw new JsonException("Expected start of tasks array");

                    var tasks = JsonSerializer.Deserialize<List<TTask>>(ref reader, options);
                    reader.Read(); // Move past end of inner array
                    reader.Read(); // Move past end of outer array

                    result.Add(new TaskGroup { Date = date, Tasks = tasks });
                }

                return result;
            }

            public override void Write(Utf8JsonWriter writer, List<TaskGroup> value, JsonSerializerOptions options)
            {
                writer.WriteStartArray();
                foreach (var group in value)
                {
                    writer.WriteStartArray();
                    writer.WriteStringValue(group.Date.ToString("O"));
                    JsonSerializer.Serialize(writer, group.Tasks, options);
                    writer.WriteEndArray();
                }

                writer.WriteEndArray();
            }
        }

        private class TaskGroup
        {
            public DateTime Date { get; set; }
            public List<TTask> Tasks { get; set; }
        }

        private class TaskGrouping : IGrouping<DateTime, TTask>
        {
            private readonly DateTime _key;
            private readonly IEnumerable<TTask> _tasks;

            public TaskGrouping(DateTime key, IEnumerable<TTask> tasks)
            {
                _key = key;
                _tasks = tasks;
            }

            public DateTime Key => _key;

            public IEnumerator<TTask> GetEnumerator() => _tasks.GetEnumerator();

            IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
        }

        private class CreateTaskRequest
        {
            public string Title { get; set; }
            public TaskPriority Priority { get; set; }
            [JsonPropertyName("parentTaskIds")]
            public string ParentTaskIds { get; set; }
        }

        private class CreateTaskWithDueRequest : CreateTaskRequest
        {
            public DateTime? DueTime { get; set; }
        }
    }
}