using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using HddtodoUI.BackendModels.JsonConverters;


namespace HddtodoUI.BackendModels.BackendStore.HttpStore
{
    public class HttpTaskTimeLogStore : HttpStoreBase, ITaskTimeLogStore
    {
        private string BaseUrl = HttpStoreBase.baseUrl;
        public event EventHandler taskTimeLogStoreSavedEvent;
        
        public HttpTaskTimeLogStore(HttpClient httpClient = null) : base(httpClient, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Converters =
            {
                new DateTimeJsonConverter(),
                new NullableDateTimeJsonConverter()
            }
        })
        {
        }

        
        
        public async Task<TaskTimeLog> CreateTaskTimeLogAsync(long taskId, long userId, string clientId = null, string clientType = null)
        {
            var query = string.IsNullOrEmpty(clientId) ? string.Empty : $"?clientId={clientId}{(string.IsNullOrEmpty(clientType) ? string.Empty : $"&clientType={clientType}")}";
            var endpoint = $"{BaseUrl}/tasks/{taskId}/timelogs/{userId}{query}";
            return await SendPostRequestAsync<TaskTimeLog>(endpoint, "Creating task time log", null);
        }
        
        public TaskTimeLog CreateTaskTimeLog(long taskId, long userId, string clientId = null, string clientType = null)
        {
            var query = string.IsNullOrEmpty(clientId) ? string.Empty : $"?clientId={clientId}{(string.IsNullOrEmpty(clientType) ? string.Empty : $"&clientType={clientType}")}";
            var endpoint = $"{BaseUrl}/tasks/{taskId}/timelogs/{userId}{query}";
            return SendPostRequestAsync<TaskTimeLog>(endpoint, "Creating task time log", null).Result;
        }

        public async Task EndTaskTimeLogAsync(TaskTimeLog log, long userId, string clientId = null)
        {
            if (log?.Id <= 0)
            {
                throw new ArgumentException("Invalid log ID", nameof(log));
            }

            var query = string.IsNullOrEmpty(clientId) ? string.Empty : $"?clientId={clientId}";
            var endpoint = $"{BaseUrl}/tasks/timelogs/{log.Id}/end/{userId}{query}";
            await SendPutRequestAsync(endpoint, "Ending task time log", null);
        }

        public void EndTaskTimeLog(TaskTimeLog log, long userId, string clientId = null)
        {
            if (log?.Id <= 0)
            {
                throw new ArgumentException("Invalid log ID", nameof(log));
            }

            var query = string.IsNullOrEmpty(clientId) ? string.Empty : $"?clientId={clientId}";
            var endpoint = $"{BaseUrl}/tasks/timelogs/{log.Id}/end/{userId}{query}";
            SendPutRequestAsync(endpoint, "Ending task time log", null).Wait();
        }

        public List<TaskTimeLog> GetTaskTimeLogs(DateTime date, long userId)
        {
            // 将日期转换为ISO 8601格式
            var isoDate = date.ToString("yyyy-MM-dd'T'HH:mm:ss.fff'Z'");
            var endpoint = $"{BaseUrl}/tasks/timelogs/date/{Uri.EscapeDataString(isoDate)}/{userId}";
            return SendGetRequestAsync<List<TaskTimeLog>>(endpoint, "Getting task time logs").Result;
        }

        
        public async Task<TaskTimeLog> CreateCompletedTaskTimeLogAsync(long taskId, long userId)
        {
            
            var endpoint = $"{BaseUrl}/tasks/{taskId}/timelogs/completed/{userId}";
            return await SendPostRequestAsync<TaskTimeLog>(endpoint, "Creating completed task time log", null);
        }
        
        public TaskTimeLog CreateCompletedTaskTimeLog(long taskId, long userId)
        {
          
            var endpoint = $"{BaseUrl}/tasks/{taskId}/timelogs/completed/{userId}";
            return SendPostRequestAsync<TaskTimeLog>(endpoint, "Creating completed task time log", null).Result;
        }

        public List<TaskTimeLogStatistics> GetTaskTimeLogStatistics(DateTime date, long userId)
        {
            var isoDate = date.ToString("yyyy-MM-dd'T'HH:mm:ss.fff'Z'");
            var endpoint = $"{BaseUrl}/tasks/timelogs/statistics/{Uri.EscapeDataString(isoDate)}/{userId}";
            return SendGetRequestAsync<List<TaskTimeLogStatistics>>(endpoint, "Getting task time log statistics").Result;
        }
    }

  
}
