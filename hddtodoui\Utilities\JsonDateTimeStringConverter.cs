using System;

namespace HddtodoUI.Utilities;

/// <summary>
/// 日期时间字符串转换工具类，与JsonConverter保持一致的格式
/// </summary>
public static class JsonDateTimeStringConverter
{
    // 统一使用的日期时间格式：yyyy-MM-dd'T'HH:mm:ss.fffzzz
    private const string DateTimeFormat = "yyyy-MM-dd'T'HH:mm:ss.fffzzz";

    /// <summary>
    /// 将可空DateTime转换为统一格式的字符串
    /// </summary>
    /// <param name="value">要转换的DateTime值</param>
    /// <returns>格式化后的字符串，如果值为null则返回null</returns>
    public static string Convert(DateTime? value)
    {
        if (!value.HasValue)
            return null;

        // 保持原有的时区信息，如果是Unspecified则视为本地时间
        DateTime outputTime = value.Value.Kind == DateTimeKind.Unspecified
            ? DateTime.SpecifyKind(value.Value, DateTimeKind.Local)
            : value.Value;

        return outputTime.ToString(DateTimeFormat);
    }

    /// <summary>
    /// 将DateTime转换为统一格式的字符串
    /// </summary>
    /// <param name="value">要转换的DateTime值</param>
    /// <returns>格式化后的字符串</returns>
    public static string Convert(DateTime value)
    {
        // 保持原有的时区信息，如果是Unspecified则视为本地时间
        DateTime outputTime = value.Kind == DateTimeKind.Unspecified
            ? DateTime.SpecifyKind(value, DateTimeKind.Local)
            : value;

        return outputTime.ToString(DateTimeFormat);
    }
}