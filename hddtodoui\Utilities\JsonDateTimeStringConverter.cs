using System;

namespace HddtodoUI.Utilities;

/// <summary>
/// 日期时间字符串转换工具类，与JsonConverter保持一致的格式
/// </summary>
public static class JsonDateTimeStringConverter
{
    // 统一使用的日期时间格式：yyyy-MM-dd'T'HH:mm:ss
    private const string DateTimeFormat = "yyyy-MM-dd'T'HH:mm:ss";

    /// <summary>
    /// 将可空DateTime转换为统一格式的字符串
    /// </summary>
    /// <param name="value">要转换的DateTime值</param>
    /// <returns>格式化后的字符串，如果值为null则返回null</returns>
    public static string Convert(DateTime? value)
    {
        if (!value.HasValue)
            return null;

        // 不包含毫秒和时区信息，使用本地时间
        return value.Value.ToString(DateTimeFormat);
    }

    /// <summary>
    /// 将DateTime转换为统一格式的字符串
    /// </summary>
    /// <param name="value">要转换的DateTime值</param>
    /// <returns>格式化后的字符串</returns>
    public static string Convert(DateTime value)
    {
        // 不包含毫秒和时区信息，使用本地时间
        return value.ToString(DateTimeFormat);
    }
}