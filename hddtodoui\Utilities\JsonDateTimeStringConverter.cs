using System;
using HddtodoUI.BackendModels.JsonConverters;

namespace HddtodoUI.Utilities;

/// <summary>
/// 日期时间字符串转换工具类，与JsonConverter保持一致的格式
/// </summary>
public static class JsonDateTimeStringConverter
{
    // 使用统一的格式常量
    private static string DateTimeFormat => DateTimeFormats.GetCurrentFormat();

    /// <summary>
    /// 将可空DateTime转换为统一格式的字符串
    /// </summary>
    /// <param name="value">要转换的DateTime值</param>
    /// <returns>格式化后的字符串，如果值为null则返回null</returns>
    public static string Convert(DateTime? value)
    {
        if (!value.HasValue)
            return null;

        // 使用统一定义的格式
        return value.Value.ToString(DateTimeFormat);
    }

    /// <summary>
    /// 将DateTime转换为统一格式的字符串
    /// </summary>
    /// <param name="value">要转换的DateTime值</param>
    /// <returns>格式化后的字符串</returns>
    public static string Convert(DateTime value)
    {
        // 使用统一定义的格式
        return value.ToString(DateTimeFormat);
    }
}