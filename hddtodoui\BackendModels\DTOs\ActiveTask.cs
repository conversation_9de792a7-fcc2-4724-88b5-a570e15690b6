using System;
using System.Text.Json.Serialization;

namespace HddtodoUI.BackendModels.DTOs
{
    public class ActiveTask
    {
        [JsonPropertyName("userId")]
        public long UserId { get; set; }

        [JsonPropertyName("clientId")]
        public string ClientId { get; set; }

        [JsonPropertyName("clientType")]
        public string ClientType { get; set; }

        [JsonPropertyName("taskId")]
        public long TaskId { get; set; }

        [JsonPropertyName("expiresAt")]
        public DateTime ExpiresAt { get; set; }
    }
}
