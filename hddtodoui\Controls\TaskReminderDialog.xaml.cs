using HddtodoUI.BackendModels.BackendStore;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using System;
using System.Collections.Generic;
using System.Linq;
using HddtodoUI.BackendModels;

namespace HddtodoUI.Controls
{
    public sealed partial class TaskReminderDialog : ContentDialog
    {
        private TaskReminder _reminder;
        private bool _isNewReminder;

        public TaskReminder Reminder => _reminder;

        public TaskReminderDialog()
        {
            this.InitializeComponent();
            InitializeDialog(null);
        }

        public TaskReminderDialog(TaskReminder reminder)
        {
            this.InitializeComponent();
            InitializeDialog(reminder);
        }

        private void InitializeDialog(TaskReminder reminder)
        {
            _isNewReminder = reminder == null;
            
            if (_isNewReminder)
            {
                // 创建新的提醒，设置默认值
                _reminder = new TaskReminder
                {
                    RemindTime = DateTime.Now.AddHours(1),
                    RepeatType = RemindRepeatType.None,
                    RepeatInterval = 1
                };
                this.CloseButtonText = "";  // 隐藏删除按钮，因为是新建提醒
            }
            else
            {
                // 编辑现有提醒
                _reminder = reminder;
            }

            // 设置UI控件的初始值
            ReminderDatePicker.Date = _reminder.RemindTime.Value.Date;
            ReminderTimePicker.Time = new TimeSpan(_reminder.RemindTime.Value.Hour, _reminder.RemindTime.Value.Minute, 0);
            
            // 设置重复类型下拉框
            int index = 0;
            switch (_reminder.RepeatType)
            {
                case RemindRepeatType.None: index = 0; break;
                case RemindRepeatType.Hourly: index = 1; break;
                case RemindRepeatType.Daily: index = 2; break;
                case RemindRepeatType.Weekly: index = 3; break;
                case RemindRepeatType.Monthly: index = 4; break;
                case RemindRepeatType.Yearly: index = 5; break;
            }
            RepeatTypeComboBox.SelectedIndex = index;

            // 设置重复间隔
            RepeatIntervalNumberBox.Value = _reminder.RepeatInterval;
            
            // 更新UI显示
            UpdateRepeatIntervalVisibility();
            UpdateNextReminderInfo();
        }

        private void RepeatTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (RepeatTypeComboBox.SelectedItem != null)
            {
                var selectedItem = RepeatTypeComboBox.SelectedItem as ComboBoxItem;
                _reminder.RepeatType = (RemindRepeatType)Enum.Parse(typeof(RemindRepeatType), selectedItem.Tag.ToString());
                
                UpdateRepeatIntervalVisibility();
                UpdateNextReminderInfo();
            }
        }

        private void UpdateRepeatIntervalVisibility()
        {
            // 只有非一次性提醒才显示重复间隔设置
            RepeatIntervalPanel.Visibility = _reminder.RepeatType == RemindRepeatType.None ? Visibility.Collapsed : Visibility.Visible;
        }

        private void UpdateNextReminderInfo()
        {
            // 获取用户选择的日期和时间
            if (_reminder == null) return;
            
            var selectedDate = ReminderDatePicker.Date.Date;
            var selectedTime = ReminderTimePicker.Time;
            var reminderDateTime = selectedDate.Add(selectedTime);
            
            // 更新提醒时间
            _reminder.RemindTime = reminderDateTime;
            
            // 更新重复间隔
            if (RepeatIntervalNumberBox.Value > 0)
            {
                _reminder.RepeatInterval = (int)RepeatIntervalNumberBox.Value;
            }
            
            // 显示下次提醒时间信息
            string repeatInfo = _reminder.GetRemindCycleDisplayInfo();
            
            NextReminderInfoTextBlock.Text = $"下次提醒时间: {_reminder.RemindTime}\n{repeatInfo}";
        }

        private void ContentDialog_CloseButtonClick(ContentDialog sender, ContentDialogButtonClickEventArgs args)
        {
            // 用户点击了删除按钮
            _reminder = null;
        }

        private void ReminderDatePicker_OnSelectedDateChanged(DatePicker sender, DatePickerSelectedValueChangedEventArgs args)
        {
            UpdateNextReminderInfo();
        }

        private void ReminderTimePicker_OnSelectedTimeChanged(TimePicker sender, TimePickerSelectedValueChangedEventArgs args)
        {
            UpdateNextReminderInfo();
        }

        private void RepeatIntervalNumberBox_OnValueChanged(NumberBox sender, NumberBoxValueChangedEventArgs args)
        {
            UpdateNextReminderInfo();
        }
    }
}
