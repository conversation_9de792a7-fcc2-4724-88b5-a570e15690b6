using System;
// JSON CONVERSION RULE (统一规范)
// 1. 所有共享枚举/日期使用 JsonPropertyName(全大写) 或 JsonConverters 目录统一实现。
// 2. 字段名必须与后端 JSON 字段完全一致，包括大小写/拼写。
// 3. 日期统一使用 yyyy-MM-dd'T'HH:mm:ss.fffzzz 格式；使用统一的JsonConverter。
// 4. 若需特殊序列化逻辑，仅在此文件或 JsonConverters 中写一次。
// 5. 其它文件只引用现有 Converter/注解，禁止重复实现。

using System.Text.Json.Serialization;
using HddtodoUI.BackendModels.JsonConverters;

namespace HddtodoUI.BackendModels
{
    public class TaskRestart
    {
        [JsonPropertyName("restartId")]
        public long RestartId { get; set; }
        
        [JsonPropertyName("taskId")]
        public long TaskId { get; set; }
        
        [JsonPropertyName("userId")]
        public long UserId { get; set; }
        
        [JsonPropertyName("taskNextRestartDateTime")]
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime TaskNextRestartDateTime { get; set; }

        [JsonPropertyName("period")]
        public TaskPeriod Period { get; set; }

        [JsonPropertyName("taskPeriodSpanCount")]
        public int TaskPeriodSpanCount { get; set; }

        [JsonPropertyName("firstSettingRestartTime")]
        [JsonConverter(typeof(NullableDateTimeJsonConverter))]
        public DateTime? FirstSettingRestartTime { get; set; }

        [JsonPropertyName("createdAt")]
        [JsonConverter(typeof(DateTimeJsonConverter))]
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }
    
    public class CreateTaskRestartRequest
    {
        [JsonPropertyName("taskId")]
        public long TaskId { get; set; }
        
        [JsonPropertyName("period")]
        public TaskPeriod Period { get; set; }
        
        [JsonPropertyName("taskPeriodSpanCount")]
        public int TaskPeriodSpanCount { get; set; }
        
        [JsonPropertyName("firstSettingRestartTime")]
        [JsonConverter(typeof(NullableDateTimeJsonConverter))]
        public DateTime? FirstSettingRestartTime { get; set; }
    }
    
    public class UpdateTaskRestartRequest
    {
        [JsonPropertyName("period")]
        public TaskPeriod Period { get; set; }
        
        [JsonPropertyName("taskPeriodSpanCount")]
        public int TaskPeriodSpanCount { get; set; }
        
        [JsonPropertyName("firstSettingRestartTime")]
        [JsonConverter(typeof(NullableDateTimeJsonConverter))]
        public DateTime? FirstSettingRestartTime { get; set; }
    }
}
