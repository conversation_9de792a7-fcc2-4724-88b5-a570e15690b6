<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="SCOPE_TYPE" value="3" />
  </component>
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile kind="Uwp">HddtodoUI.csproj</projectFile>
    <projectFile profileName="HddtodoUI (Unpackaged)">HddtodoUI.csproj</projectFile>
    <projectFile pubXmlPath="Properties/PublishProfiles/win-arm64.pubxml">HddtodoUI.csproj</projectFile>
    <projectFile pubXmlPath="Properties/PublishProfiles/win-x64.pubxml">HddtodoUI.csproj</projectFile>
    <projectFile pubXmlPath="Properties/PublishProfiles/win-x86.pubxml">HddtodoUI.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="98100042-6353-424d-839f-165be5762b68" name="Changes" comment="Changes">
      <change beforePath="$PROJECT_DIR$/App.config" beforeDir="false" afterPath="$PROJECT_DIR$/App.config" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackendModels/DTOs/ActiveTask.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackendModels/DTOs/ActiveTask.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackendModels/JsonConverters/DateTimeJsonConverter.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackendModels/JsonConverters/DateTimeJsonConverter.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackendModels/JsonConverters/NullableDateTimeJsonConverter.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackendModels/JsonConverters/NullableDateTimeJsonConverter.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackendModels/TTask.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackendModels/TTask.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackendModels/TaskCategory.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackendModels/TaskCategory.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackendModels/TaskList.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackendModels/TaskList.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackendModels/TaskReminder.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackendModels/TaskReminder.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackendModels/TaskRestart.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackendModels/TaskRestart.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackendModels/TaskStep.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackendModels/TaskStep.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/BackendModels/TaskTimeLog.cs" beforeDir="false" afterPath="$PROJECT_DIR$/BackendModels/TaskTimeLog.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Utilities/JsonDateTimeStringConverter.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Utilities/JsonDateTimeStringConverter.cs" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="Git.Settings">
    <option name="PREVIEW_PUSH_PROTECTED_ONLY" value="true" />
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="main" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$USER_HOME$/.nuget/packages/microsoft.windowsappsdk/1.7.250208002-preview1/include/UndockedRegFreeWinRT-AutoInitializer.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/.nuget/packages/microsoft.windowsappsdk/1.7.250401001/include/WindowsAppRuntimeAutoInitializer.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$USER_HOME$/AppData/Local/Symbols/src/dotnet/runtime/08338fcaa5c9b9a8190abb99222fed12aaba956c/src/libraries/System.Private.CoreLib/src/System/Enum.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/24372012c6c94138bf3f496e104366ffc8f918/c2/e36b8d19/PortableThreadPool.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/24372012c6c94138bf3f496e104366ffc8f918/f7/783959e0/EventArgs.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5148e388a7db4994ad2ab3750386454116e910/d2/29824528/JsonSerializerOptions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5aeb6167299d4cfba30c86624130edbcc90938/95/95ad521f/EventHandler`1.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/611af054b2b04e5cb1ff23f4bc6b445ef61920/b5/63b52ed1/MessageBoxOptions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6403c8fd18394ebdad3cbad3ec0b805e185a20/98/6a8a87fa/DispatcherQueueHandler.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6403c8fd18394ebdad3cbad3ec0b805e185a20/a3/aba7cc08/DispatcherQueueSynchronizationContext.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6bf0add349bc4899ae73e111249c8e256f7648/07/3bedc3bb/_EventSource_global__Windows_Foundation_TypedEvent.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6bf0add349bc4899ae73e111249c8e256f7648/1e/778af054/ItemsControl.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6bf0add349bc4899ae73e111249c8e256f7648/6f/38b1ddb8/Grid.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6bf0add349bc4899ae73e111249c8e256f7648/72/40c6fddc/VisualTreeHelper.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6bf0add349bc4899ae73e111249c8e256f7648/81/34542cd8/_EventSource_global__Microsoft_UI_Xaml_DragEventHa.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6bf0add349bc4899ae73e111249c8e256f7648/95/96c24e52/TreeViewDragItemsCompletedEventArgs.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6bf0add349bc4899ae73e111249c8e256f7648/9b/01de0654/ITreeViewItemTemplateSettingsMethods.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6bf0add349bc4899ae73e111249c8e256f7648/c5/496579d4/UIElement.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6bf0add349bc4899ae73e111249c8e256f7648/c8/0826a048/TreeViewItemTemplateSettings.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6bf0add349bc4899ae73e111249c8e256f7648/d0/c80836cf/TreeViewItem.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6bf0add349bc4899ae73e111249c8e256f7648/e6/36c92a50/GroupStyle.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/a1f219ec84e444f49934c60501a0b6aa13910/30/529395f9/ObservableCollection`1.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/a1f219ec84e444f49934c60501a0b6aa13910/75/d854b8aa/PropertyChangedEventArgs.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e703623710cc408eaac0f2c6feb0a00c81230/78/ebb3c126/IListMethods`1.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e703623710cc408eaac0f2c6feb0a00c81230/c0/fe93bf36/CastExtensions.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e703623710cc408eaac0f2c6feb0a00c81230/e9/78bac74a/ExceptionHelpers.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/2e49d132ea68ff47071c64476ae29138fa965dc555ef7c3f4991586431de241/Task.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/6f90c6c13479b8c2a5be98f3d75dfc3bd885a055652d8a32904ca2448132949e/Future.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/7f19a1c771a4827e95b66ae9db63df0b2f38a59147517f2fb2af634b594f069/HttpListener.Windows.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/9eda537f15ea23cdfae523c19e87eb303a3ded88937ae7e55919387a43f70/ExecutionContext.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/b7208b3f72528d22781d25fde9a55271bdf2b5aade4f03b1324579a25493cd8/List.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/b86cdfe19de105ff2eab7d5d4c613fd91b9adfe4f95bd82e2d6db593e6d3ca3/TaskContinuation.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/SourcesCache/fb367341658aaedd7de424d4c9189db33c4387e268dcc4011918569442ab12/AsyncTaskMethodBuilderT.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Services/NotificationService.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="mock://D:/netProject/newhddtodoui/hddtodoui/Utilities/JsonDateTimeStringConverter.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Windows/NotificationWindow.xaml.cs" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2u8IsgSe1YHI25CzxvggbOPF3JT" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;.NET Launch Settings Profile.HddtodoUI: HddtodoUI (Unpackaged).executor&quot;: &quot;Debug&quot;,
    &quot;.NET 启动设置配置文件.HddtodoUI: HddtodoUI (Unpackaged).executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;UWP.HddtodoUI.executor&quot;: &quot;Debug&quot;,
    &quot;XThreadsFramesViewSplitterKey&quot;: &quot;0.56013983&quot;,
    &quot;com.codeium.enabled&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.codeium.AppSettingsConfigurable&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;postgresql&quot;
    ]
  }
}</component>
  <component name="RunManager" selected=".NET 启动设置配置文件.HddtodoUI: HddtodoUI (Unpackaged)">
    <configuration name="HddtodoUI: win-arm64" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="win-arm64.pubxml" pubxml_path="$PROJECT_DIR$/Properties/PublishProfiles/win-arm64.pubxml" uuid_high="-8269576565106261160" uuid_low="-9156782702145970298" />
      <method v="2" />
    </configuration>
    <configuration name="HddtodoUI: win-x64" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="win-x64.pubxml" pubxml_path="$PROJECT_DIR$/Properties/PublishProfiles/win-x64.pubxml" uuid_high="-8269576565106261160" uuid_low="-9156782702145970298" />
      <method v="2" />
    </configuration>
    <configuration name="HddtodoUI: win-x86" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish publish_profile="win-x86.pubxml" pubxml_path="$PROJECT_DIR$/Properties/PublishProfiles/win-x86.pubxml" uuid_high="-8269576565106261160" uuid_low="-9156782702145970298" />
      <method v="2" />
    </configuration>
    <configuration name="HddtodoUI: HddtodoUI (Unpackaged)" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/HddtodoUI.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net8.0-windows10.0.22621.0" />
      <option name="LAUNCH_PROFILE_NAME" value="HddtodoUI (Unpackaged)" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="HddtodoUI" type="UwpProject" factoryName="UWP">
      <option name="PROJECT_PATH" value="$PROJECT_DIR$/HddtodoUI.csproj" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="98100042-6353-424d-839f-165be5762b68" name="Changes" comment="" />
      <created>1741623983325</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1741623983325</updated>
      <workItem from="1741623985355" duration="1304000" />
      <workItem from="1741682283319" duration="11682000" />
      <workItem from="1741694047461" duration="6628000" />
      <workItem from="1741704755359" duration="65000" />
      <workItem from="1741758196623" duration="9004000" />
      <workItem from="1741768699769" duration="8424000" />
      <workItem from="1741786477069" duration="1178000" />
      <workItem from="1741825227423" duration="9702000" />
      <workItem from="1741845333359" duration="9358000" />
      <workItem from="1741856175288" duration="3928000" />
      <workItem from="1741871700526" duration="7008000" />
      <workItem from="1741913715450" duration="7363000" />
      <workItem from="1741928626427" duration="900000" />
      <workItem from="1741953401476" duration="6865000" />
      <workItem from="1741960387375" duration="1482000" />
      <workItem from="1741962046273" duration="214000" />
      <workItem from="1741994734962" duration="14002000" />
      <workItem from="1742046621820" duration="3983000" />
      <workItem from="1742051144447" duration="224000" />
      <workItem from="1742055986115" duration="737000" />
      <workItem from="1742106195379" duration="13098000" />
      <workItem from="1742120981553" duration="10377000" />
      <workItem from="1742176591269" duration="15329000" />
      <workItem from="1742224326949" duration="3308000" />
      <workItem from="1742263633848" duration="21134000" />
      <workItem from="1742343712526" duration="5648000" />
      <workItem from="1742356938979" duration="23977000" />
      <workItem from="1742436639751" duration="4124000" />
      <workItem from="1742440910907" duration="24934000" />
      <workItem from="1742520723584" duration="23176000" />
      <workItem from="1742630461776" duration="171000" />
      <workItem from="1742643104990" duration="1551000" />
      <workItem from="1742655757351" duration="987000" />
      <workItem from="1742695744326" duration="2871000" />
      <workItem from="1742730894050" duration="1028000" />
      <workItem from="1742783074803" duration="758000" />
      <workItem from="1742890577730" duration="2372000" />
      <workItem from="1743075763325" duration="13446000" />
      <workItem from="1743131350237" duration="6693000" />
      <workItem from="1743153615028" duration="70000" />
      <workItem from="1743164515682" duration="10064000" />
      <workItem from="1743298972378" duration="8555000" />
      <workItem from="1743337350897" duration="1277000" />
      <workItem from="1743346601491" duration="3856000" />
      <workItem from="1743399135582" duration="18816000" />
      <workItem from="1743427419665" duration="2652000" />
      <workItem from="1743430406777" duration="834000" />
      <workItem from="1743476125632" duration="20578000" />
      <workItem from="1743561268142" duration="25358000" />
      <workItem from="1743647545450" duration="29421000" />
      <workItem from="1743729892597" duration="9739000" />
      <workItem from="1743859621098" duration="148000" />
      <workItem from="1743859821275" duration="3764000" />
      <workItem from="1743908323863" duration="12107000" />
      <workItem from="1744095552798" duration="9928000" />
      <workItem from="1744251485974" duration="104000" />
      <workItem from="1744251609385" duration="84000" />
      <workItem from="1744251861824" duration="69000" />
      <workItem from="1744251944554" duration="8964000" />
      <workItem from="1744337036441" duration="1985000" />
      <workItem from="1744339683807" duration="553000" />
      <workItem from="1744342948925" duration="3377000" />
      <workItem from="1744510511340" duration="694000" />
      <workItem from="1744515359748" duration="12470000" />
      <workItem from="1744556246606" duration="28000" />
      <workItem from="1744557652204" duration="835000" />
      <workItem from="1744558841342" duration="4444000" />
      <workItem from="1744766772481" duration="5042000" />
      <workItem from="1744780989991" duration="7996000" />
      <workItem from="1744857823544" duration="1966000" />
      <workItem from="1744865619847" duration="7794000" />
      <workItem from="1745162308136" duration="2526000" />
      <workItem from="1745544224468" duration="59000" />
      <workItem from="1745544321781" duration="1324000" />
      <workItem from="1745546550263" duration="1297000" />
      <workItem from="1745837066178" duration="5086000" />
      <workItem from="1746153339398" duration="71000" />
      <workItem from="1746153498443" duration="2515000" />
      <workItem from="1746175031434" duration="2702000" />
      <workItem from="1746258121170" duration="5746000" />
      <workItem from="1746631429996" duration="1028000" />
      <workItem from="1746691527768" duration="10569000" />
      <workItem from="1746756503287" duration="10459000" />
      <workItem from="1746865045509" duration="9942000" />
      <workItem from="1747034700652" duration="558000" />
      <workItem from="1747035329981" duration="1704000" />
      <workItem from="1747139343657" duration="6482000" />
      <workItem from="1747188281661" duration="4355000" />
      <workItem from="1747227256789" duration="6874000" />
      <workItem from="1747271170495" duration="33640000" />
      <workItem from="1747358068116" duration="19428000" />
      <workItem from="1747443342993" duration="2782000" />
      <workItem from="1747466162359" duration="12353000" />
      <workItem from="1747488464385" duration="628000" />
      <workItem from="1747556109331" duration="15366000" />
      <workItem from="1747622776590" duration="10697000" />
      <workItem from="1747704069652" duration="8686000" />
      <workItem from="1747736841199" duration="12550000" />
      <workItem from="1747792308309" duration="16024000" />
      <workItem from="1747871253727" duration="5873000" />
      <workItem from="1747879535554" duration="23000" />
      <workItem from="1747880104512" duration="3616000" />
      <workItem from="1747908397818" duration="17069000" />
      <workItem from="1747958065093" duration="24194000" />
      <workItem from="1748054614263" duration="3869000" />
      <workItem from="1748088049611" duration="725000" />
      <workItem from="1748088796943" duration="936000" />
      <workItem from="1748092936087" duration="3409000" />
      <workItem from="1748099173260" duration="4666000" />
      <workItem from="1748138762828" duration="78000" />
      <workItem from="1748138896889" duration="2726000" />
      <workItem from="1748151156355" duration="11274000" />
      <workItem from="1748179564337" duration="5021000" />
      <workItem from="1748226157778" duration="8907000" />
      <workItem from="1748254408297" duration="12375000" />
      <workItem from="1748310550517" duration="13864000" />
      <workItem from="1748530105379" duration="3699000" />
      <workItem from="1748617629835" duration="10399000" />
      <workItem from="1748704670209" duration="9003000" />
      <workItem from="1748785173847" duration="4170000" />
      <workItem from="1748789788682" duration="1593000" />
      <workItem from="1748791577376" duration="854000" />
      <workItem from="1748828685087" duration="13460000" />
      <workItem from="1748913381363" duration="13445000" />
      <workItem from="1748997127392" duration="15864000" />
      <workItem from="1749127538232" duration="942000" />
      <workItem from="1749128739744" duration="2736000" />
      <workItem from="1749176270004" duration="17106000" />
      <workItem from="1749261974870" duration="4182000" />
      <workItem from="1749550862676" duration="720000" />
      <workItem from="1749623648084" duration="10130000" />
      <workItem from="1749696110342" duration="1415000" />
      <workItem from="1749710283734" duration="6782000" />
      <workItem from="1749780201239" duration="4025000" />
      <workItem from="1749869774243" duration="8791000" />
      <workItem from="1750042250896" duration="5769000" />
      <workItem from="1750489370652" duration="19563000" />
      <workItem from="1750557813118" duration="2522000" />
      <workItem from="1750682795355" duration="4897000" />
      <workItem from="1750762260207" duration="10618000" />
      <workItem from="1750809040686" duration="2025000" />
      <workItem from="1750857072173" duration="9371000" />
      <workItem from="1750903722314" duration="13952000" />
      <workItem from="1751000212538" duration="3812000" />
      <workItem from="1751031747617" duration="752000" />
      <workItem from="1751079485908" duration="1779000" />
      <workItem from="1751082634233" duration="5264000" />
      <workItem from="1751160005319" duration="12993000" />
      <workItem from="1751202296623" duration="4018000" />
      <workItem from="1751209573578" duration="1055000" />
      <workItem from="1751290679973" duration="2346000" />
      <workItem from="1751293292120" duration="3368000" />
      <workItem from="1751340095327" duration="12982000" />
      <workItem from="1751373993922" duration="958000" />
      <workItem from="1751377324786" duration="1834000" />
      <workItem from="1751380005303" duration="1673000" />
      <workItem from="1751419888513" duration="9794000" />
    </task>
    <task id="LOCAL-00249" summary="解决indicator切换模式后 标题过长时 时间 超出 窗口的bug 今天">
      <option name="closed" value="true" />
      <created>1748792109988</created>
      <option name="number" value="00249" />
      <option name="presentableId" value="LOCAL-00249" />
      <option name="project" value="LOCAL" />
      <updated>1748792109988</updated>
    </task>
    <task id="LOCAL-00250" summary="重构步骤taskstep的后台reposition代码，代替原来复杂的算法">
      <option name="closed" value="true" />
      <created>1748832354152</created>
      <option name="number" value="00250" />
      <option name="presentableId" value="LOCAL-00250" />
      <option name="project" value="LOCAL" />
      <updated>1748832354152</updated>
    </task>
    <task id="LOCAL-00251" summary="任务步骤加上右键 编辑 菜单">
      <option name="closed" value="true" />
      <created>1748833775062</created>
      <option name="number" value="00251" />
      <option name="presentableId" value="LOCAL-00251" />
      <option name="project" value="LOCAL" />
      <updated>1748833775062</updated>
    </task>
    <task id="LOCAL-00252" summary="任务步骤加上颜色设置">
      <option name="closed" value="true" />
      <created>1748834070203</created>
      <option name="number" value="00252" />
      <option name="presentableId" value="LOCAL-00252" />
      <option name="project" value="LOCAL" />
      <updated>1748834070203</updated>
    </task>
    <task id="LOCAL-00253" summary="选择下一个任务 也加上home按钮和seperator">
      <option name="closed" value="true" />
      <created>1748842325687</created>
      <option name="number" value="00253" />
      <option name="presentableId" value="LOCAL-00253" />
      <option name="project" value="LOCAL" />
      <updated>1748842325687</updated>
    </task>
    <task id="LOCAL-00254" summary="task修改duetime不成功的bug">
      <option name="closed" value="true" />
      <created>1748866381372</created>
      <option name="number" value="00254" />
      <option name="presentableId" value="LOCAL-00254" />
      <option name="project" value="LOCAL" />
      <updated>1748866381372</updated>
    </task>
    <task id="LOCAL-00255" summary="解决taskstep的一些bug">
      <option name="closed" value="true" />
      <created>1748916162854</created>
      <option name="number" value="00255" />
      <option name="presentableId" value="LOCAL-00255" />
      <option name="project" value="LOCAL" />
      <updated>1748916162854</updated>
    </task>
    <task id="LOCAL-00256" summary="把details窗口右边弄得更紧凑一些">
      <option name="closed" value="true" />
      <created>1748938514444</created>
      <option name="number" value="00256" />
      <option name="presentableId" value="LOCAL-00256" />
      <option name="project" value="LOCAL" />
      <updated>1748938514444</updated>
    </task>
    <task id="LOCAL-00257" summary="把details窗口弄得更紧凑一些">
      <option name="closed" value="true" />
      <created>1748939883078</created>
      <option name="number" value="00257" />
      <option name="presentableId" value="LOCAL-00257" />
      <option name="project" value="LOCAL" />
      <updated>1748939883078</updated>
    </task>
    <task id="LOCAL-00258" summary="在system分类也就是系统视图里面，查看已完成分类不应该出现">
      <option name="closed" value="true" />
      <created>1748997567827</created>
      <option name="number" value="00258" />
      <option name="presentableId" value="LOCAL-00258" />
      <option name="project" value="LOCAL" />
      <updated>1748997567828</updated>
    </task>
    <task id="LOCAL-00259" summary="当当前分类下已完成子分类为0时不显示 已完成任务分类 expander">
      <option name="closed" value="true" />
      <created>1748998473849</created>
      <option name="number" value="00259" />
      <option name="presentableId" value="LOCAL-00259" />
      <option name="project" value="LOCAL" />
      <updated>1748998473849</updated>
    </task>
    <task id="LOCAL-00260" summary="展开expander的时候，expander的head的字体透明度变回正常">
      <option name="closed" value="true" />
      <created>1749001699568</created>
      <option name="number" value="00260" />
      <option name="presentableId" value="LOCAL-00260" />
      <option name="project" value="LOCAL" />
      <updated>1749001699568</updated>
    </task>
    <task id="LOCAL-00261" summary="再快速添加任务对话框加上语音识别">
      <option name="closed" value="true" />
      <created>1749039203220</created>
      <option name="number" value="00261" />
      <option name="presentableId" value="LOCAL-00261" />
      <option name="project" value="LOCAL" />
      <updated>1749039203220</updated>
    </task>
    <task id="LOCAL-00262" summary="taskstep的 check的事件绑定导致数据紊乱的bu">
      <option name="closed" value="true" />
      <created>1749130870357</created>
      <option name="number" value="00262" />
      <option name="presentableId" value="LOCAL-00262" />
      <option name="project" value="LOCAL" />
      <updated>1749130870357</updated>
    </task>
    <task id="LOCAL-00263" summary="客户端 去掉 taskdetail 里面 备注 scrollview的 横向 滚动条 让 备注可以自动换行 今天">
      <option name="closed" value="true" />
      <created>1749177069677</created>
      <option name="number" value="00263" />
      <option name="presentableId" value="LOCAL-00263" />
      <option name="project" value="LOCAL" />
      <updated>1749177069677</updated>
    </task>
    <task id="LOCAL-00264" summary="indicatorwindow的最近执行的任务 改成二级菜单，并且加上 编辑 和重新执行 ，初步实现，重新执行这个动作还没有做">
      <option name="closed" value="true" />
      <created>1749193019257</created>
      <option name="number" value="00264" />
      <option name="presentableId" value="LOCAL-00264" />
      <option name="project" value="LOCAL" />
      <updated>1749193019257</updated>
    </task>
    <task id="LOCAL-00265" summary="indicatorwindow的最近执行的任务 改成二级菜单，并且加上 编辑 和重新执行">
      <option name="closed" value="true" />
      <created>1749195071192</created>
      <option name="number" value="00265" />
      <option name="presentableId" value="LOCAL-00265" />
      <option name="project" value="LOCAL" />
      <updated>1749195071192</updated>
    </task>
    <task id="LOCAL-00266" summary="用try catch+task 来尝试 async void 的解决">
      <option name="closed" value="true" />
      <created>1749195429180</created>
      <option name="number" value="00266" />
      <option name="presentableId" value="LOCAL-00266" />
      <option name="project" value="LOCAL" />
      <updated>1749195429180</updated>
    </task>
    <task id="LOCAL-00267" summary="修改了一下菜单名称">
      <option name="closed" value="true" />
      <created>1749195600293</created>
      <option name="number" value="00267" />
      <option name="presentableId" value="LOCAL-00267" />
      <option name="project" value="LOCAL" />
      <updated>1749195600293</updated>
    </task>
    <task id="LOCAL-00268" summary="任务暂停时或者完成时 自动返回到任务选择界面 改成 只有完成时自动返回到任务选择界面,完成和暂停分开设置">
      <option name="closed" value="true" />
      <created>1749206156047</created>
      <option name="number" value="00268" />
      <option name="presentableId" value="LOCAL-00268" />
      <option name="project" value="LOCAL" />
      <updated>1749206156047</updated>
    </task>
    <task id="LOCAL-00269" summary="修正了一下dragdropwindowhelper里面的方法名">
      <option name="closed" value="true" />
      <created>1749262697694</created>
      <option name="number" value="00269" />
      <option name="presentableId" value="LOCAL-00269" />
      <option name="project" value="LOCAL" />
      <updated>1749262697694</updated>
    </task>
    <task id="LOCAL-00270" summary="用config可以来配置是否启动伴生窗口">
      <option name="closed" value="true" />
      <created>1749628264391</created>
      <option name="number" value="00270" />
      <option name="presentableId" value="LOCAL-00270" />
      <option name="project" value="LOCAL" />
      <updated>1749628264391</updated>
    </task>
    <task id="LOCAL-00271" summary="调整一下默认值">
      <option name="closed" value="true" />
      <created>1749629361690</created>
      <option name="number" value="00271" />
      <option name="presentableId" value="LOCAL-00271" />
      <option name="project" value="LOCAL" />
      <updated>1749629361690</updated>
    </task>
    <task id="LOCAL-00272" summary="重构json codec的一些问题，并规范化">
      <option name="closed" value="true" />
      <created>1749650320035</created>
      <option name="number" value="00272" />
      <option name="presentableId" value="LOCAL-00272" />
      <option name="project" value="LOCAL" />
      <updated>1749650320035</updated>
    </task>
    <task id="LOCAL-00273" summary="indicatorwindow 加上毛玻璃效果">
      <option name="closed" value="true" />
      <created>1749716269988</created>
      <option name="number" value="00273" />
      <option name="presentableId" value="LOCAL-00273" />
      <option name="project" value="LOCAL" />
      <updated>1749716269988</updated>
    </task>
    <task id="LOCAL-00274" summary="taskstepwindow也要放在窗口最前面">
      <option name="closed" value="true" />
      <created>1749782823508</created>
      <option name="number" value="00274" />
      <option name="presentableId" value="LOCAL-00274" />
      <option name="project" value="LOCAL" />
      <updated>1749782823508</updated>
    </task>
    <task id="LOCAL-00275" summary="解决indicator window 当前任务 双击 出详情 感觉困难的问题">
      <option name="closed" value="true" />
      <created>1749870668577</created>
      <option name="number" value="00275" />
      <option name="presentableId" value="LOCAL-00275" />
      <option name="project" value="LOCAL" />
      <updated>1749870668577</updated>
    </task>
    <task id="LOCAL-00276" summary="taskstepwindow的打开关闭逻辑要优化">
      <option name="closed" value="true" />
      <created>1749872682271</created>
      <option name="number" value="00276" />
      <option name="presentableId" value="LOCAL-00276" />
      <option name="project" value="LOCAL" />
      <updated>1749872682271</updated>
    </task>
    <task id="LOCAL-00277" summary="添加子分类有一个bug，父分类的圆点符号没有及时更新为&gt;号">
      <option name="closed" value="true" />
      <created>1750046048699</created>
      <option name="number" value="00277" />
      <option name="presentableId" value="LOCAL-00277" />
      <option name="project" value="LOCAL" />
      <updated>1750046048699</updated>
    </task>
    <task id="LOCAL-00278" summary="同步的取进行中的项目 计划 count的也加上了，需要重构">
      <option name="closed" value="true" />
      <created>1750781083388</created>
      <option name="number" value="00278" />
      <option name="presentableId" value="LOCAL-00278" />
      <option name="project" value="LOCAL" />
      <updated>1750781083388</updated>
    </task>
    <task id="LOCAL-00279" summary="taskcategory plan 增加了 对 子分类的支持">
      <option name="closed" value="true" />
      <created>1750917034798</created>
      <option name="number" value="00279" />
      <option name="presentableId" value="LOCAL-00279" />
      <option name="project" value="LOCAL" />
      <updated>1750917034798</updated>
    </task>
    <task id="LOCAL-00280" summary="父 路径 加上空白处理得更易读">
      <option name="closed" value="true" />
      <created>1750917170720</created>
      <option name="number" value="00280" />
      <option name="presentableId" value="LOCAL-00280" />
      <option name="project" value="LOCAL" />
      <updated>1750917170720</updated>
    </task>
    <task id="LOCAL-00281" summary="限时完成的 项目 加上 日期颜色指示">
      <option name="closed" value="true" />
      <created>1750918873054</created>
      <option name="number" value="00281" />
      <option name="presentableId" value="LOCAL-00281" />
      <option name="project" value="LOCAL" />
      <updated>1750918873054</updated>
    </task>
    <task id="LOCAL-00282" summary="完成和未完成任务的操作加上布尔值确认回执">
      <option name="closed" value="true" />
      <created>1751003249697</created>
      <option name="number" value="00282" />
      <option name="presentableId" value="LOCAL-00282" />
      <option name="project" value="LOCAL" />
      <updated>1751003249698</updated>
    </task>
    <task id="LOCAL-00283" summary="switchcompelte方法加上出错提示">
      <option name="closed" value="true" />
      <created>1751160100210</created>
      <option name="number" value="00283" />
      <option name="presentableId" value="LOCAL-00283" />
      <option name="project" value="LOCAL" />
      <updated>1751160100210</updated>
    </task>
    <task id="LOCAL-00284" summary="优化编辑任务细节保存 未保存提示体验,初步完成逻辑，但是还是有bug">
      <option name="closed" value="true" />
      <created>1751208526861</created>
      <option name="number" value="00284" />
      <option name="presentableId" value="LOCAL-00284" />
      <option name="project" value="LOCAL" />
      <updated>1751208526862</updated>
    </task>
    <task id="LOCAL-00285" summary="再次优化一下：优化编辑任务细节保存 未保存提示体验">
      <option name="closed" value="true" />
      <created>1751208937167</created>
      <option name="number" value="00285" />
      <option name="presentableId" value="LOCAL-00285" />
      <option name="project" value="LOCAL" />
      <updated>1751208937167</updated>
    </task>
    <task id="LOCAL-00286" summary="限时完成的项目 里面的 分类 要可以点击 初步完成 项目条的 ui调整">
      <option name="closed" value="true" />
      <created>1751210029002</created>
      <option name="number" value="00286" />
      <option name="presentableId" value="LOCAL-00286" />
      <option name="project" value="LOCAL" />
      <updated>1751210029002</updated>
    </task>
    <task id="LOCAL-00287" summary="初步实现了 限时完成的项目 里面的 分类 要可以点击，顶层treeview分类可以显示被选择，但是子分类的还需要做">
      <option name="closed" value="true" />
      <created>1751294206676</created>
      <option name="number" value="00287" />
      <option name="presentableId" value="LOCAL-00287" />
      <option name="project" value="LOCAL" />
      <updated>1751294206676</updated>
    </task>
    <task id="LOCAL-00288" summary="限时完成的项目 里面的 分类 要可以点击 子分类也可以被选择上了">
      <option name="closed" value="true" />
      <created>1751295271430</created>
      <option name="number" value="00288" />
      <option name="presentableId" value="LOCAL-00288" />
      <option name="project" value="LOCAL" />
      <updated>1751295271430</updated>
    </task>
    <task id="LOCAL-00289" summary="解决任务没有载入的bug">
      <option name="closed" value="true" />
      <created>1751296082038</created>
      <option name="number" value="00289" />
      <option name="presentableId" value="LOCAL-00289" />
      <option name="project" value="LOCAL" />
      <updated>1751296082038</updated>
    </task>
    <task id="LOCAL-00290" summary="TaskSystemCategorySelected加上了检查是不是systemcategory的检查">
      <option name="closed" value="true" />
      <created>1751296361009</created>
      <option name="number" value="00290" />
      <option name="presentableId" value="LOCAL-00290" />
      <option name="project" value="LOCAL" />
      <updated>1751296361009</updated>
    </task>
    <task id="LOCAL-00291" summary="修改category的时间后的处理">
      <option name="closed" value="true" />
      <created>1751345190878</created>
      <option name="number" value="00291" />
      <option name="presentableId" value="LOCAL-00291" />
      <option name="project" value="LOCAL" />
      <updated>1751345190879</updated>
    </task>
    <task id="LOCAL-00292" summary="限时完成的项目 加上 项目的任务数和子分类数">
      <option name="closed" value="true" />
      <created>1751349282798</created>
      <option name="number" value="00292" />
      <option name="presentableId" value="LOCAL-00292" />
      <option name="project" value="LOCAL" />
      <updated>1751349282798</updated>
    </task>
    <task id="LOCAL-00293" summary="上次提交漏了一个">
      <option name="closed" value="true" />
      <created>1751354109025</created>
      <option name="number" value="00293" />
      <option name="presentableId" value="LOCAL-00293" />
      <option name="project" value="LOCAL" />
      <updated>1751354109025</updated>
    </task>
    <task id="LOCAL-00294" summary="完善限时完成的项目 加上 完成改项目 的菜单">
      <option name="closed" value="true" />
      <created>1751354121461</created>
      <option name="number" value="00294" />
      <option name="presentableId" value="LOCAL-00294" />
      <option name="project" value="LOCAL" />
      <updated>1751354121461</updated>
    </task>
    <task id="LOCAL-00295" summary="taskstep加上复制taskstep标题的右键菜单">
      <option name="closed" value="true" />
      <created>1751354813774</created>
      <option name="number" value="00295" />
      <option name="presentableId" value="LOCAL-00295" />
      <option name="project" value="LOCAL" />
      <updated>1751354813775</updated>
    </task>
    <task id="LOCAL-00296" summary="在 限时完成的项目 点 完成后 ，左边treeview也要去掉这个分类">
      <option name="closed" value="true" />
      <created>1751378693291</created>
      <option name="number" value="00296" />
      <option name="presentableId" value="LOCAL-00296" />
      <option name="project" value="LOCAL" />
      <updated>1751378693291</updated>
    </task>
    <task id="LOCAL-00297" summary="1、在 限时完成的项目 点 完成后 ，左边treeview也要去掉这个分类 2、在 限时完成的项目 点 完成后 ，左边 system category的计数要更新">
      <option name="closed" value="true" />
      <created>1751379022132</created>
      <option name="number" value="00297" />
      <option name="presentableId" value="LOCAL-00297" />
      <option name="project" value="LOCAL" />
      <updated>1751379022132</updated>
    </task>
    <option name="localTasksCounter" value="298" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="HEAD" />
                </option>
              </RecentGroup>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="subtaskbranch" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
    <MESSAGE value="indicatorwindow 加上毛玻璃效果" />
    <MESSAGE value="taskstepwindow也要放在窗口最前面" />
    <MESSAGE value="解决indicator window 当前任务 双击 出详情 感觉困难的问题" />
    <MESSAGE value="taskstepwindow的打开关闭逻辑要优化" />
    <MESSAGE value="添加子分类有一个bug，父分类的圆点符号没有及时更新为&gt;号" />
    <MESSAGE value="同步的取进行中的项目 计划 count的也加上了，需要重构" />
    <MESSAGE value="taskcategory plan 增加了 对 子分类的支持" />
    <MESSAGE value="父 路径 加上空白处理得更易读" />
    <MESSAGE value="限时完成的 项目 加上 日期颜色指示" />
    <MESSAGE value="完成和未完成任务的操作加上布尔值确认回执" />
    <MESSAGE value="switchcompelte方法加上出错提示" />
    <MESSAGE value="优化编辑任务细节保存 未保存提示体验,初步完成逻辑，但是还是有bug" />
    <MESSAGE value="再次优化一下：优化编辑任务细节保存 未保存提示体验" />
    <MESSAGE value="限时完成的项目 里面的 分类 要可以点击 初步完成 项目条的 ui调整" />
    <MESSAGE value="初步实现了 限时完成的项目 里面的 分类 要可以点击，顶层treeview分类可以显示被选择，但是子分类的还需要做" />
    <MESSAGE value="限时完成的项目 里面的 分类 要可以点击 子分类也可以被选择上了" />
    <MESSAGE value="解决任务没有载入的bug" />
    <MESSAGE value="TaskSystemCategorySelected加上了检查是不是systemcategory的检查" />
    <MESSAGE value="修改category的时间后的处理" />
    <MESSAGE value="限时完成的项目 加上 项目的任务数和子分类数" />
    <MESSAGE value="上次提交漏了一个" />
    <MESSAGE value="完善限时完成的项目 加上 完成改项目 的菜单" />
    <MESSAGE value="taskstep加上复制taskstep标题的右键菜单" />
    <MESSAGE value="在 限时完成的项目 点 完成后 ，左边treeview也要去掉这个分类" />
    <MESSAGE value="1、在 限时完成的项目 点 完成后 ，左边treeview也要去掉这个分类 2、在 限时完成的项目 点 完成后 ，左边 system category的计数要更新" />
    <option name="LAST_COMMIT_MESSAGE" value="1、在 限时完成的项目 点 完成后 ，左边treeview也要去掉这个分类 2、在 限时完成的项目 点 完成后 ，左边 system category的计数要更新" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="31" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="32" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="33" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="LaunchSettings">
        <watch expression="targetItem" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>